#!/usr/bin/env python3
"""
Skyblock Outpost Event Countdown Timer

This application calculates and maintains a countdown to the next Skyblock Outpost event.
Events occur every 6 hours starting at 11:00 PM PST on July 29, 2025.

The countdown is written to a text file that can be read by external applications.
While CustomHUD cannot directly read external files, this creates the foundation
for integration with custom mods or server plugins.

Author: Generated for CustomHUD Integration
Date: 2025-07-30
"""

import datetime
import pytz
import time
import logging
import os
import sys
from pathlib import Path

# Configuration
# Default CustomHUD profile path - modify this to match your Minecraft installation
DEFAULT_CUSTOMHUD_PATH = r"C:\Users\<USER>\AppData\Roaming\.minecraft\config\custom-hud\profiles\timer.txt"
# Fallback to local directory if CustomHUD directory doesn't exist
LOCAL_FALLBACK_PATH = "timer.txt"

OUTPUT_FILE = DEFAULT_CUSTOMHUD_PATH
LOG_FILE = "outpost_countdown.log"
UPDATE_INTERVAL = 60  # seconds
EVENT_INTERVAL_HOURS = 6

# Event schedules
# Skyblock events start July 29, 2025 at 11:00 PM PST
SKYBLOCK_OUTPOST_START = datetime.datetime(2025, 7, 29, 23, 0, 0)  # 11:00 PM
SKYBLOCK_POND_START = datetime.datetime(2025, 7, 30, 2, 0, 0)     # 2:00 AM (3 hours after outpost)

# Lifesteal events start July 30, 2025 at 2:00 AM PST
LIFESTEAL_OUTPOST_START = datetime.datetime(2025, 7, 30, 2, 0, 0)  # 2:00 AM
LIFESTEAL_POND_START = datetime.datetime(2025, 7, 30, 1, 0, 0)     # 1:00 AM (1 hour before outpost)

PST_TIMEZONE = pytz.timezone('US/Pacific')

def setup_logging():
    """Configure logging for the application."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(LOG_FILE),
            logging.StreamHandler(sys.stdout)
        ]
    )

def get_next_event_time(current_time_pst, start_date, interval_hours):
    """
    Calculate the next event time based on a recurring schedule.

    Args:
        current_time_pst: Current time in PST timezone
        start_date: Event start date (datetime object)
        interval_hours: Hours between events

    Returns:
        datetime: Next event time in PST
    """
    # Localize the start date to PST
    start_time_pst = PST_TIMEZONE.localize(start_date)

    # Calculate how much time has passed since the start
    time_since_start = current_time_pst - start_time_pst

    # If we're before the start date, return the start date
    if time_since_start.total_seconds() < 0:
        return start_time_pst

    # Calculate how many complete intervals have passed
    interval_seconds = interval_hours * 3600
    intervals_passed = int(time_since_start.total_seconds() // interval_seconds)

    # Calculate the next event time
    next_event = start_time_pst + datetime.timedelta(seconds=(intervals_passed + 1) * interval_seconds)

    return next_event

def get_skyblock_outpost_next(current_time_pst):
    """Get next Skyblock Outpost event (every 6 hours)."""
    return get_next_event_time(current_time_pst, SKYBLOCK_OUTPOST_START, 6)

def get_skyblock_pond_next(current_time_pst):
    """Get next Skyblock Pond event (every 6 hours, 3 hours offset from Outpost)."""
    return get_next_event_time(current_time_pst, SKYBLOCK_POND_START, 6)

def get_lifesteal_outpost_next(current_time_pst):
    """Get next Lifesteal Outpost event (every 3 hours)."""
    return get_next_event_time(current_time_pst, LIFESTEAL_OUTPOST_START, 3)

def get_lifesteal_pond_next(current_time_pst):
    """Get next Lifesteal Pond event (every 3 hours, 1 hour before Outpost)."""
    return get_next_event_time(current_time_pst, LIFESTEAL_POND_START, 3)

def get_dynamic_color(time_remaining):
    """
    Get the appropriate color code based on time remaining.

    Args:
        time_remaining: timedelta object representing time until next event

    Returns:
        str: CustomHUD color code
    """
    total_seconds = int(time_remaining.total_seconds())

    if total_seconds <= 0:
        return "&a"  # Green for event starting
    elif total_seconds <= 300:  # 5 minutes or less
        return "&a"  # Green for imminent events
    elif total_seconds >= (6 * 3600 - 600):  # Within 10 minutes of reset (for 6-hour events)
        return "&6"  # Orange for recently reset events
    elif total_seconds >= (3 * 3600 - 600):  # Within 10 minutes of reset (for 3-hour events)
        return "&6"  # Orange for recently reset events
    else:
        return "&7"  # Gray for normal countdown

def format_countdown_with_color(time_remaining, event_name, emoji=""):
    """
    Format the time remaining with dynamic colors and emojis.

    Args:
        time_remaining: timedelta object representing time until next event
        event_name: Name of the event (e.g., "Outpost", "Pond")
        emoji: Optional emoji to display

    Returns:
        str: Formatted countdown string with colors
    """
    total_seconds = int(time_remaining.total_seconds())
    color = get_dynamic_color(time_remaining)

    if total_seconds <= 0:
        return f"{color}{emoji}{event_name}: &l&aEVENT STARTING!"

    hours = total_seconds // 3600
    minutes = (total_seconds % 3600) // 60

    # Handle singular vs plural
    hour_text = "h" if hours == 1 else "h"
    minute_text = "m" if minutes == 1 else "m"

    if hours > 0 and minutes > 0:
        return f"{color}{emoji}{event_name}: {hours}{hour_text} {minutes}{minute_text}"
    elif hours > 0:
        return f"{color}{emoji}{event_name}: {hours}{hour_text}"
    elif minutes > 0:
        return f"{color}{emoji}{event_name}: {minutes}{minute_text}"
    else:
        return f"{color}{emoji}{event_name}: &l<1m"

def create_customhud_profile(countdown_data):
    """
    Create a complete CustomHUD profile configuration with multi-server countdowns.

    Args:
        countdown_data: Dictionary containing formatted countdown strings for all events

    Returns:
        str: Complete CustomHUD profile configuration
    """
    profile_content = f"""==Section:TopLeft==
&6&l⚔ Skyblock
{countdown_data['skyblock_outpost']}
{countdown_data['skyblock_pond']}

&c&l⚡ Lifesteal
{countdown_data['lifesteal_outpost']}
{countdown_data['lifesteal_pond']}
"""
    return profile_content

def write_countdown_to_file(countdown_data):
    """
    Write the countdown data as a complete CustomHUD profile with error handling.
    Falls back to local directory if CustomHUD directory is not accessible.

    Args:
        countdown_data: Dictionary containing formatted countdown strings for all events
    """
    global OUTPUT_FILE

    try:
        # Ensure the directory exists
        profile_dir = os.path.dirname(OUTPUT_FILE)
        os.makedirs(profile_dir, exist_ok=True)

        # Create the complete CustomHUD profile
        profile_content = create_customhud_profile(countdown_data)

        with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
            f.write(profile_content)
            f.flush()  # Ensure data is written immediately
        logging.debug(f"Successfully wrote CustomHUD profile with multi-server countdowns")

    except (PermissionError, FileNotFoundError, OSError) as e:
        # Fall back to local directory if CustomHUD directory is not accessible
        if OUTPUT_FILE != LOCAL_FALLBACK_PATH:
            logging.warning(f"Cannot write to CustomHUD directory: {e}")
            logging.warning(f"Falling back to local file: {LOCAL_FALLBACK_PATH}")
            OUTPUT_FILE = LOCAL_FALLBACK_PATH

            try:
                profile_content = create_customhud_profile(countdown_data)
                with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
                    f.write(profile_content)
                    f.flush()
                logging.info(f"Successfully wrote to fallback file: {OUTPUT_FILE}")
            except Exception as fallback_error:
                logging.error(f"Failed to write to fallback file: {fallback_error}")
        else:
            logging.error(f"Failed to write to file {OUTPUT_FILE}: {e}")
    except Exception as e:
        logging.error(f"Unexpected error writing to file: {e}")

def calculate_all_countdowns():
    """
    Main function to calculate all countdowns and update the output file.
    """
    try:
        # Get current time in PST
        pst = pytz.timezone('US/Pacific')
        now_utc = datetime.datetime.now(pytz.UTC)
        now_pst = now_utc.astimezone(pst)

        # Calculate next event times for all events
        skyblock_outpost_next = get_skyblock_outpost_next(now_pst)
        skyblock_pond_next = get_skyblock_pond_next(now_pst)
        lifesteal_outpost_next = get_lifesteal_outpost_next(now_pst)
        lifesteal_pond_next = get_lifesteal_pond_next(now_pst)

        # Calculate time remaining for each event
        skyblock_outpost_remaining = skyblock_outpost_next - now_pst
        skyblock_pond_remaining = skyblock_pond_next - now_pst
        lifesteal_outpost_remaining = lifesteal_outpost_next - now_pst
        lifesteal_pond_remaining = lifesteal_pond_next - now_pst

        # Format all countdowns with colors and emojis
        countdown_data = {
            'skyblock_outpost': format_countdown_with_color(skyblock_outpost_remaining, "Outpost", "⚔ "),
            'skyblock_pond': format_countdown_with_color(skyblock_pond_remaining, "Pond", "🎣 "),
            'lifesteal_outpost': format_countdown_with_color(lifesteal_outpost_remaining, "Outpost", "⚔ "),
            'lifesteal_pond': format_countdown_with_color(lifesteal_pond_remaining, "Pond", "🎣 ")
        }

        # Write to file
        write_countdown_to_file(countdown_data)

        # Log the update
        logging.info("Updated CustomHUD profile with all countdowns")
        logging.debug(f"Current PST time: {now_pst.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        logging.debug(f"Skyblock Outpost: {skyblock_outpost_next.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        logging.debug(f"Skyblock Pond: {skyblock_pond_next.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        logging.debug(f"Lifesteal Outpost: {lifesteal_outpost_next.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        logging.debug(f"Lifesteal Pond: {lifesteal_pond_next.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        logging.debug(f"Profile written to: {OUTPUT_FILE}")

    except pytz.exceptions.UnknownTimeZoneError as e:
        logging.error(f"Timezone error: {e}")
    except Exception as e:
        logging.error(f"Unexpected error in countdown calculation: {e}")

# Keep the old function name for backward compatibility
def calculate_outpost_countdown():
    """Backward compatibility wrapper."""
    calculate_all_countdowns()

def print_event_schedule():
    """Print the event schedule for reference."""
    print("\n" + "="*70)
    print("MULTI-SERVER EVENT COUNTDOWN - CUSTOMHUD INTEGRATION")
    print("="*70)
    print("⚔ SKYBLOCK EVENTS (Every 6 hours):")
    print("  Outpost: 11PM, 5AM, 11AM, 5PM PST")
    print("  Pond:    2AM, 8AM, 2PM, 8PM PST")
    print()
    print("⚡ LIFESTEAL EVENTS (Every 3 hours):")
    print("  Outpost: 2AM, 5AM, 8AM, 11AM, 2PM, 5PM, 8PM, 11PM PST")
    print("  Pond:    1AM, 4AM, 7AM, 10AM, 1PM, 4PM, 7PM, 10PM PST")
    print("="*70)
    print(f"CustomHUD Profile: {OUTPUT_FILE}")
    print(f"Log file: {LOG_FILE}")
    print(f"Update interval: {UPDATE_INTERVAL} seconds")
    print("="*70)
    print("DYNAMIC COLORS:")
    print("🟢 Green: Event starting (≤5 min) or just started (≤10 min)")
    print("🟠 Orange: Recently reset events")
    print("⚪ Gray: Normal countdown")
    print("="*70)
    print("USAGE INSTRUCTIONS:")
    print("1. Make sure this timer is running")
    print("2. In Minecraft, open Mod Menu → CustomHud → Settings")
    print("3. Click 'Edit Profile 1/2/3' and select the timer.txt file")
    print("4. All four countdowns will appear in your HUD automatically!")
    print("="*70 + "\n")

def main():
    """Main application entry point."""
    # Setup logging
    setup_logging()
    
    # Print schedule information
    print_event_schedule()
    
    logging.info("Starting Skyblock Outpost Countdown Timer - CustomHUD Integration")
    logging.info(f"CustomHUD Profile: {OUTPUT_FILE}")
    logging.info(f"Update interval: {UPDATE_INTERVAL} seconds")
    
    # Initial countdown calculation
    calculate_all_countdowns()

    try:
        # Main loop
        while True:
            time.sleep(UPDATE_INTERVAL)
            calculate_all_countdowns()
            
    except KeyboardInterrupt:
        logging.info("Countdown timer stopped by user")
        print("\nCountdown timer stopped.")
    except Exception as e:
        logging.error(f"Fatal error in main loop: {e}")
        print(f"Fatal error: {e}")
    finally:
        # Write final message to profile
        try:
            final_data = {
                'skyblock_outpost': "&8Timer Stopped",
                'skyblock_pond': "&8Timer Stopped",
                'lifesteal_outpost': "&8Timer Stopped",
                'lifesteal_pond': "&8Timer Stopped"
            }
            write_countdown_to_file(final_data)
        except:
            pass

if __name__ == "__main__":
    main()
